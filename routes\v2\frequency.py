from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    OCR_PLATFORM_FREQUENCY,
    LLM_PLATFORM_FREQUENCY_AND_SUMMARY,
    DEFAULT_LLM_REASONING_ENABLED,
)
from services.frequency_service_v2 import get_keyword_count_v2
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from utils.enums import OCRType, CloudPlatform, EndpointDescriptions, FrequencyPrompt
from utils.cloud_utils import (
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)
from typing import Dict, Any

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(OCR_PLATFORM_FREQUENCY, "/v2/frequency")
LLM_PLATFORM = get_endpoint_llm_platform(
    LLM_PLATFORM_FREQUENCY_AND_SUMMARY, "/v2/frequency"
)
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    DEFAULT_LLM_REASONING_ENABLED, "/v2/frequency"
)

frequency_router = APIRouter()


@frequency_router.post(
    "/v2/frequency",
    tags=["v2"],
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.frequency_v2,
)
async def frequency(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    keywords: str = Form(..., description="Comma separated list of keywords."),
    frequency_prompt: FrequencyPrompt = Form(
        None,
        description="Optional: Prompt for AI on how to analyze the context for each keyword.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform for context analysis, could be 'azure' or 'aws'. By default {LLM_PLATFORM}.",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        description=f"Optional: Enable LLM reasoning. By default {LLM_REASONING}.",
        enum=["true", "false"],
    ),
) -> Dict[str, Any]:
    """
    Calculate the frequency of keywords in the PDF document with context analysis.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        keywords (str): Comma separated list of keywords.
        frequency_prompt (FrequencyPrompt, optional): The prompt for AI on how to analyze the context for each keyword.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        ocr_type (OCRType, optional): The OCR type.
        llm_platform (CloudPlatform, optional): The LLM platform for context analysis.
        llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: The frequency of the keywords in the PDF document with context analysis.
    """
    logging.info(f"ocr_type: {ocr_type}")
    logging.info(f"llm_platform: {llm_platform}")

    if frequency_prompt:
        frequency_prompt = frequency_prompt.frequency_prompt
    else:
        frequency_prompt = FrequencyPrompt().frequency_prompt

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )

    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    logging.info("Calculating Frequencies with Context Analysis.")
    response = await get_keyword_count_v2(
        document_json, keywords, frequency_prompt, llm_platform, llm_reasoning_enabled
    )

    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )

    return response
