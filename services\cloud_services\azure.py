from openai import AzureOpenAI
from azure.ai.formrecognizer import DocumentAnalysisClient
from azure.core.credentials import AzureKeyCredential
from langchain_openai import AzureChatOpenAI
from constants import (
    RAG_SYSTEM_PROMPT,
    AZURE_OPEN_AI_DEPLOYMENT_NAME,
    AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME,
    SUMMARY_TEMPERATURE,
    AZURE_OPEN_AI_API_KEY,
    AZURE_OPEN_AI_API_VERSION,
    AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME,
    AZURE_COGNITIVE_SEARCH_ENDPOINT,
    AZURE_COGNITIVE_SEARCH_API_KEY,
    AZURE_OPEN_AI_ENDPOINT,
    SUMMARY_SEED,
)
import logging
from services.embeddings.custom_azure_openai_embeddings import (
    CustomAzureOpenAIEmbeddings,
)
from utils.enums import OCRType, CloudPlatform
from typing import Type
from pydantic import BaseModel
from langchain.output_parsers import PydanticToolsParser
from langchain_community.callbacks import get_openai_callback
from langchain_core.prompts import ChatPromptTemplate


class AzureHelper:
    """
    A helper class that interacts with Azure services to perform tasks
    like generating responses, and running OCR.
    """

    def __init__(self):
        """
        Initializes the AzureHelper class with the cloud name.
        """
        self.cloud_name = CloudPlatform.azure

    def get_llm_response_with_metadata(
        self,
        query_prompt: str,
        response_format: str = "text",
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - query_prompt (str): The user's query.
            - response_format (str): The format of the response (default is 'text').
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - dict: A dictionary containing the response content and token usage.
        """
        azure_openai_client = self._get_open_ai_client()
        if llm_reasoning_enabled == "true":
            azure_deployment = AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME
            temperature = 1.0  # Reasoning model temperature can only be 1.0
            system_message = {"role": "user", "content": system_prompt}
            if AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME == "o1-mini":
                response_format = "text"
        else:
            azure_deployment = AZURE_OPEN_AI_DEPLOYMENT_NAME
            system_message = {"role": "system", "content": system_prompt}
        gpt_response = azure_openai_client.chat.completions.create(
            model=azure_deployment,
            response_format={"type": response_format},
            messages=[
                system_message,
                {"role": "user", "content": query_prompt},
            ],
        )
        response = {
            "content": gpt_response.choices[0].message.content,
            "total_tokens": gpt_response.usage.total_tokens,
            "prompt_tokens": gpt_response.usage.prompt_tokens,
            "completion_tokens": gpt_response.usage.completion_tokens,
        }
        return response

    def get_llm_pydantic_schema_response_with_metadata(
        self,
        question_prompt: str,
        output_pydantic_schema: Type[BaseModel],
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - question_prompt (str): The user's query.
            - output_pydantic_schema (str): The output pydantic schema.
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - dict: A dictionary containing the response content and token usage.
        """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{question_prompt}"),
            ]
        )
        chat_model = self.get_chat_model(llm_reasoning_enabled)

        llm_with_tools = chat_model.bind_tools([output_pydantic_schema])
        query_analyzer = (
            prompt
            | llm_with_tools
            | PydanticToolsParser(tools=[output_pydantic_schema])
        )

        with get_openai_callback() as callback:
            llm_response = query_analyzer.invoke({"question_prompt": question_prompt})
            tokens_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }

        return {"llm_response": llm_response, "tokens_usage": tokens_usage}

    async def get_llm_pydantic_schema_response_with_metadata_async(
        self,
        question_prompt: str,
        output_pydantic_schema: Type[BaseModel],
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - question_prompt (str): The user's query.
            - output_pydantic_schema (str): The output pydantic schema.
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - dict: A dictionary containing the response content and token usage.
        """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{question_prompt}"),
            ]
        )
        chat_model = self.get_chat_model(llm_reasoning_enabled)

        llm_with_tools = chat_model.bind_tools([output_pydantic_schema])
        query_analyzer = (
            prompt
            | llm_with_tools
            | PydanticToolsParser(tools=[output_pydantic_schema])
        )

        with get_openai_callback() as callback:
            llm_response = await query_analyzer.ainvoke(
                {"question_prompt": question_prompt}
            )
            tokens_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }

        return {"llm_response": llm_response, "tokens_usage": tokens_usage}

    def get_chat_model(self, llm_reasoning_enabled: str = "false") -> AzureChatOpenAI:
        """
        Initializes and returns an AzureChatOpenAI model with specified parameters.
        Returns:
            - AzureChatOpenAI: The AzureChatOpenAI instance with configured settings.
        """
        if llm_reasoning_enabled == "true":
            temperature = 1.0  # Reasoning model temperature can only be 1.0
            azure_deployment = AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME
        else:
            temperature = SUMMARY_TEMPERATURE
            azure_deployment = AZURE_OPEN_AI_DEPLOYMENT_NAME

        model = AzureChatOpenAI(
            openai_api_key=AZURE_OPEN_AI_API_KEY,
            azure_endpoint=AZURE_OPEN_AI_ENDPOINT,
            openai_api_version=AZURE_OPEN_AI_API_VERSION,
            azure_deployment=azure_deployment,
            temperature=temperature,
            seed=SUMMARY_SEED,
        )
        return model

    def get_embedding_model(self) -> CustomAzureOpenAIEmbeddings:
        """
        Loads the custom Azure OpenAI embeddings.
        Returns:
            - CustomAzureOpenAIEmbeddings: An instance of the custom embeddings class.
        """
        embeddings = CustomAzureOpenAIEmbeddings(
            azure_deployment=AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPEN_AI_API_VERSION,
        )
        return embeddings

    def get_extracted_text_from_image(
        self,
        image: bytes,
        page_no: int,
        image_width: int = None,
        image_height: int = None,
    ) -> tuple:
        """
        Runs Optical Character Recognition (OCR) on a given document page using Azure Cognitive Services.
        Args:
            - image (bytes): The document page data for OCR.
            - page_no (int): The page number.
            - image_width (int): The width of the image.
            - image_height (int): The height of the image.
        Returns:
            - tuple: A tuple containing the OCR result and the page number.
        """
        document_analysis_client = self._get_document_analysis_client()
        logging.info("Using prebuilt-read model for OCR.")
        poller = document_analysis_client.begin_analyze_document(
            model_id="prebuilt-read", document=image
        )
        return (poller.result().to_dict(), page_no)

    def get_formatted_ocr_response(self, document_text_list: list) -> dict:
        """
        Format the OCR dictionary data into the required format.
        Args:
            - document_text_list (list): The document pages data for OCR.
        Returns:
            - dict: A dictionary containing the OCR result (as dict) in the required format.
        """
        document_text_json = {}
        for response in document_text_list:
            page_num = response[1]
            document_text_json[page_num] = {}
            document_text_json[page_num]["content"] = (
                response[0].get("content", "").replace("\n", " ")
            )
            image_data = response[0]["pages"][0]
            document_text_json[page_num]["info"] = self._format_azure_data(
                image_data["words"]
            )
            document_text_json[page_num]["image_size"] = {
                "width": int(image_data["width"]),
                "height": int(image_data["height"]),
            }
            document_text_json[page_num]["angle"] = image_data["angle"]
            document_text_json[page_num]["extraction"] = OCRType.Azure
        return document_text_json

    def _format_azure_data(self, words_data: list) -> list:
        """
        Format the Azure OCR data into the required format.
        Args:
            - words_data (list): The words data from the OCR.
        Returns:
            - list: A list of word bounding boxes and their content.
        """
        word_box_info = []
        for word in words_data:
            vertices_bb = []
            for point in word["polygon"]:
                x = int((point["x"]))
                y = int(point["y"])
                vertices_bb.append((x, y))
            if len(vertices_bb) > 0:
                left_most = min(vertices_bb[0][0], vertices_bb[3][0])
                right_most = max(vertices_bb[1][0], vertices_bb[2][0])
                top_most = min(vertices_bb[0][1], vertices_bb[2][1])
                bottom_most = max(vertices_bb[2][1], vertices_bb[3][1])
                word_box_info.append(
                    [[left_most, top_most, right_most, bottom_most], word["content"]]
                )
        return word_box_info

    def _get_document_analysis_client(self) -> DocumentAnalysisClient:
        """
        Private method to initialize and return the DocumentAnalysisClient.
        Returns:
            - DocumentAnalysisClient: An instance of the DocumentAnalysisClient class.
        """
        return DocumentAnalysisClient(
            endpoint=AZURE_COGNITIVE_SEARCH_ENDPOINT,
            credential=AzureKeyCredential(AZURE_COGNITIVE_SEARCH_API_KEY),
        )

    def _get_open_ai_client(self) -> AzureOpenAI:
        """
        Private method to initialize and return the AzureOpenAI.
        Returns:
            - AzureOpenAI: An instance of the AzureOpenAI class.
        """
        return AzureOpenAI(
            azure_endpoint=AZURE_OPEN_AI_ENDPOINT,
            api_key=AZURE_OPEN_AI_API_KEY,
            api_version=AZURE_OPEN_AI_API_VERSION,
        )
