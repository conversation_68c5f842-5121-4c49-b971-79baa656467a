from constants import (
    BEDROCK_EMBEDDING_MODEL_ID,
    BED<PERSON>CK_LLM_MODEL_ID,
    SUMMARY_TEMPERATURE,
    RAG_SYSTEM_PROMPT,
)
from langchain_aws import ChatBedrock
import boto3
import logging
from utils.enums import OCRType, CloudPlatform
from services.embeddings.custom_bedrock_embeddings import CustomBedrockEmbeddings
from typing import Type
from pydantic import BaseModel
from langchain.output_parsers import PydanticToolsParser
from langchain_community.callbacks.manager import get_bedrock_anthropic_callback
from langchain_core.prompts import ChatPromptTemplate


class AWSHelper:
    """
    A helper class that interacts with AWS services to perform tasks
    like generating responses, and running OCR.
    """

    def __init__(self):
        """
        Initializes the AWSHelper class with the cloud name.
        """
        self.cloud_name = CloudPlatform.aws

    def get_chat_model(self, llm_reasoning_enabled: str = "false") -> ChatBedrock:
        """
        Method to return the chat model.
        Args:
            llm_reasoning_enabled (str): Whether to enable LLM reasoning.
        Returns:
            ChatBedrock: The ChatBedrock instance configured with model_id and kwargs.
        """
        model = ChatBedrock(
            client=boto3.client(service_name="bedrock-runtime"),
            model_id=BEDROCK_LLM_MODEL_ID,
            model_kwargs={
                "temperature": SUMMARY_TEMPERATURE,
                "max_tokens": 4096,
            },
        )
        return model

    def get_llm_response_with_metadata(
        self,
        query_prompt: str,
        response_format: str = "text",
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from the AWS Bedrock model based on the provided query prompt.
        Args:
            query_prompt (str): The user's query.
            response_format (str): The format of the response (default is 'text').
            system_prompt (str): System prompt to guide the AI model's behavior.
            llm_reasoning_enabled (str): Whether to enable LLM reasoning.
        Returns:
            dict: A dictionary containing the response content and token usage.
        """
        messages = [
            (
                "system",
                system_prompt,
            ),
            ("human", query_prompt),
        ]

        llm = self.get_chat_model()

        # Invoke the model with the constructed messages
        ai_msg = llm.invoke(messages)

        response = {
            "content": ai_msg.content,
            "total_tokens": ai_msg.response_metadata["usage"]["total_tokens"],
            "prompt_tokens": ai_msg.response_metadata["usage"]["prompt_tokens"],
            "completion_tokens": ai_msg.response_metadata["usage"]["completion_tokens"],
        }

        return response

    def get_llm_pydantic_schema_response_with_metadata(
        self,
        question_prompt: str,
        output_pydantic_schema: Type[BaseModel],
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - question_prompt (str): The user's query.
            - output_pydantic_schema (str): The output pydantic schema.
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - dict: A dictionary containing the response content and token usage.
        """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{question_prompt}"),
            ]
        )
        chat_model = self.get_chat_model(llm_reasoning_enabled)

        llm_with_tools = chat_model.bind_tools([output_pydantic_schema])
        query_analyzer = (
            prompt
            | llm_with_tools
            | PydanticToolsParser(tools=[output_pydantic_schema])
        )

        with get_bedrock_anthropic_callback() as callback:
            llm_response = query_analyzer.invoke({"question_prompt": question_prompt})
            tokens_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }

        return {"llm_response": llm_response, "tokens_usage": tokens_usage}

    async def get_llm_pydantic_schema_response_with_metadata_async(
        self,
        question_prompt: str,
        output_pydantic_schema: Type[BaseModel],
        system_prompt: str = RAG_SYSTEM_PROMPT,
        llm_reasoning_enabled: str = "false",
    ) -> dict:
        """
        Generates a response from Azure OpenAI based on the provided query prompt.
        Args:
            - question_prompt (str): The user's query.
            - output_pydantic_schema (str): The output pydantic schema.
            - system_prompt (str): System prompt to guide the AI model's behavior.
        Returns:
            - dict: A dictionary containing the response content and token usage.
        """
        prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("human", "{question_prompt}"),
            ]
        )
        chat_model = self.get_chat_model(llm_reasoning_enabled)

        llm_with_tools = chat_model.bind_tools([output_pydantic_schema])
        query_analyzer = (
            prompt
            | llm_with_tools
            | PydanticToolsParser(tools=[output_pydantic_schema])
        )

        with get_bedrock_anthropic_callback() as callback:
            llm_response = await query_analyzer.ainvoke(
                {"question_prompt": question_prompt}
            )
            tokens_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }

        return {"llm_response": llm_response, "tokens_usage": tokens_usage}

    def get_embedding_model(self) -> CustomBedrockEmbeddings:
        """
        Loads the custom Amazon Bedrock embeddings.
        Returns:
            - CustomBedrockEmbeddings: An instance of the custom bedrock embeddings class.
        """
        embeddings = CustomBedrockEmbeddings(
            client=boto3.client(service_name="bedrock-runtime"),
            model_id=BEDROCK_EMBEDDING_MODEL_ID,
        )
        return embeddings

    def get_extracted_text_from_image(
        self, image: bytes, page_no: int, image_width: int, image_height: int
    ) -> tuple:
        """
        This function sends the image data to Amazon Textract to extract text from the image.
        Args:
            - image: Byte data of the image to be processed.
            - page_no: The page number corresponding to the image.
            - image_width: The width of the image.
            - image_height: The height of the image.
        Returns:
            - tuple: A tuple containing the OCR result and the page number.
        """
        try:
            textract_client = self._get_textract_client()
            ocr_reponse = textract_client.detect_document_text(
                Document={"Bytes": image}
            )
            ocr_reponse["image_size"] = {
                "width": int(image_width),
                "height": int(image_height),
            }
            return (ocr_reponse, page_no)
        except Exception as e:
            logging.exception(f"Error while doing OCR using AWS for page:{page_no}")
            raise e

    def get_formatted_ocr_response(self, document_text_list: list) -> dict:
        """
        This function formats the raw document_text_list from Amazon Textract into a structured JSON format.
        Args:
            - document_text_list: List of OCR results from Textract, with each item containing OCR data and page number.
        Returns:
            - dict: A dictionary with formatted OCR results, including the content, bounding box info, and image size.
        """
        document_text_json = {}
        for response in document_text_list:
            ocr_response = response[0]
            page_no = response[1]

            document_text_json[page_no] = {
                "content": "",
                "info": [],
                "image_size": {},
                "angle": 0,
                "extraction": OCRType.AWS,
            }
            if "Blocks" not in ocr_response:
                continue

            for block in ocr_response["Blocks"]:
                if "LINE" == block.get("BlockType"):
                    document_text_json[page_no]["content"] += " " + block["Text"]
                if "WORD" == block.get("BlockType"):
                    pixels = self._extract_pixels(
                        block,
                        ocr_response["image_size"]["width"],
                        ocr_response["image_size"]["height"],
                    )
                    document_text_json[page_no]["info"].append(
                        [
                            pixels,
                            block["Text"],
                        ]
                    )
            document_text_json[page_no]["image_size"] = ocr_response["image_size"]
        return document_text_json

    def _extract_pixels(self, block: dict, image_width: int, image_height: int) -> list:
        """
        This private function extracts pixel coordinates of the bounding box for a block of text detected by Textract.
        Args:
            - block: A block object returned from Textract that contains bounding box info.
            - image_width: The width of the image being processed.
            - image_height: The height of the image being processed.
        Returns:
            - list: A list of pixel coordinates corresponding to the block's bounding box.
        """
        points = None
        for coordinate in block["Geometry"]["Polygon"]:
            if not points:
                points = [
                    coordinate["X"],
                    coordinate["Y"],
                    coordinate["X"],
                    coordinate["Y"],
                ]
            else:
                points[0] = min(points[0], coordinate["X"])  # x1
                points[1] = min(points[1], coordinate["Y"])  # y1
                points[2] = max(points[2], coordinate["X"])  # x2
                points[3] = max(points[3], coordinate["Y"])  # y2

        # Converting coordinate to pixels
        pixels = []
        pixels.append(int(points[0] * image_width))
        pixels.append(int(points[1] * image_height))
        pixels.append(int(points[2] * image_width))
        pixels.append(int(points[3] * image_height))
        return pixels

    def _get_textract_client(self) -> boto3.client:
        """
        Private method to initialize and return the Textract client when needed.
        Returns:
            - boto3.client: The Textract client.
        """
        return boto3.client(service_name="textract")
