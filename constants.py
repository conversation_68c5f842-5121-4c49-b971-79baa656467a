import os

# Azure OpenAI and OCR Keys
AZURE_OPEN_AI_ENDPOINT = os.getenv("AZURE_OPEN_AI_ENDPOINT")
AZURE_OPEN_AI_API_KEY = os.getenv("AZURE_OPEN_AI_API_KEY")
AZURE_OPEN_AI_API_VERSION = os.getenv("AZURE_OPEN_AI_API_VERSION", "2024-12-01-preview")
AZURE_OPEN_AI_DEPLOYMENT_NAME = os.getenv("AZURE_OPEN_AI_DEPLOYMENT_NAME")
AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME = os.getenv(
    "AZURE_OPEN_AI_REASONING_DEPLOYMENT_NAME"
)
AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME = os.getenv(
    "AZURE_OPEN_AI_EMBEDDING_DEPLOYMENT_NAME", "text-embedding-3-large"
)
AZURE_COGNITIVE_SEARCH_API_KEY = os.getenv("AZURE_COGNITIVE_SEARCH_API_KEY")
AZURE_COGNITIVE_SEARCH_ENDPOINT = os.getenv("AZURE_COGNITIVE_SEARCH_ENDPOINT")

# Setting for AzureOpenAIEmbeddings
os.environ["AZURE_OPENAI_API_KEY"] = str(AZURE_OPEN_AI_API_KEY)
os.environ["AZURE_OPENAI_ENDPOINT"] = str(AZURE_OPEN_AI_ENDPOINT)

# Authentication
DISABLE_IP_WHITELIST = os.getenv("DISABLE_IP_WHITELIST", "false").lower()
IP_WHITELIST = set(
    [ip.strip() for ip in os.getenv("IP_WHITELIST", "127.0.0.1").split(",")]
)
DISABLE_BASIC_AUTH = os.getenv("DISABLE_BASIC_AUTH", "false").lower()
BASIC_AUTH_KEY = os.getenv("BASIC_AUTH_KEY")

# Workers
LAMBDA_WORKERS = int(os.getenv("LAMBDA_WORKERS", "100"))
AZURE_WORKERS = int(os.getenv("AZURE_WORKERS", "50"))
AWS_TEXTRACT_WORKERS = int(os.getenv("AWS_TEXTRACT_WORKERS", "10"))
LLM_WORKERS = int(os.getenv("LLM_WORKERS", "15"))
PDF_TO_IMAGE_WORKERS = int(os.getenv("PDF_TO_IMAGE_WORKERS", "15"))
RAG_WORKERS = int(os.getenv("RAG_WORKERS", "50"))
EMBEDDING_WORKERS = int(os.getenv("EMBEDDING_WORKERS", "15"))
VECTOR_STORE_WORKERS = int(os.getenv("VECTOR_STORE_WORKERS", "15"))

# Document Chunks
SUMMARY_DOCUMENT_CHUNK_WORD_COUNT = int(
    os.getenv("SUMMARY_DOCUMENT_CHUNK_WORD_COUNT", "4000")
)
SUMMARY_CHUNK_OVERLAPPING_WORD_COUNT = int(
    os.getenv("SUMMARY_CHUNK_OVERLAPPING_WORD_COUNT", "50")
)
REDUCE_STEP_CHUNK_WORD_COUNT = int(os.getenv("REDUCE_STEP_CHUNK_WORD_COUNT", "4000"))
EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT = int(
    os.getenv("EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT", "100")
)
EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT = int(
    os.getenv("EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT", "10")
)
PARENT_CHUNK_WORD_COUNT = int(os.getenv("PARENT_CHUNK_WORD_COUNT", "500"))
PARENT_CHUNK_OVERLAPPING_WORD_COUNT = int(
    os.getenv("PARENT_CHUNK_OVERLAPPING_WORD_COUNT", "0")
)
NUMBER_OF_RETRIEVED_PARENT_CHUNKS = int(
    os.getenv("NUMBER_OF_RETRIEVED_PARENT_CHUNKS", "3")
)
ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED = os.getenv(
    "ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED", "false"
)
PARENT_CHUNK_ENABLED = os.getenv("PARENT_CHUNK_ENABLED ", "true")

# Bedrock
BEDROCK_LLM_MODEL_ID = os.getenv(
    "BEDROCK_LLM_MODEL_ID", "anthropic.claude-3-haiku-20240307-v1:0"
)
BEDROCK_EMBEDDING_MODEL_ID = os.getenv(
    "BEDROCK_EMBEDDING_MODEL_ID", "amazon.titan-embed-text-v2:0"
)

# Cloud Platforms for Model Selection
OCR_PLATFORM_OCR = os.getenv("OCR_PLATFORM_OCR")
OCR_PLATFORM_OCR_AND_EMBEDDING = os.getenv("OCR_PLATFORM_OCR_AND_EMBEDDING")
EMBEDDING_PLATFORM_OCR_AND_EMBEDDING = os.getenv("EMBEDDING_PLATFORM_OCR_AND_EMBEDDING")
OCR_PLATFORM_FREQUENCY = os.getenv("OCR_PLATFORM_FREQUENCY")
OCR_PLATFORM_QA = os.getenv("OCR_PLATFORM_QA")
LLM_PLATFORM_QA = os.getenv("LLM_PLATFORM_QA")
EMBEDDING_PLATFORM_QA = os.getenv("EMBEDDING_PLATFORM_QA")
OCR_PLATFORM_DOCUMENT_CATEGORIZATION = os.getenv("OCR_PLATFORM_DOCUMENT_CATEGORIZATION")
LLM_PLATFORM_DOCUMENT_CATEGORIZATION = os.getenv("LLM_PLATFORM_DOCUMENT_CATEGORIZATION")
EMBEDDING_PLATFORM_DOCUMENT_CATEGORIZATION = os.getenv(
    "EMBEDDING_PLATFORM_DOCUMENT_CATEGORIZATION"
)
OCR_PLATFORM_SUMMARIZE = os.getenv("OCR_PLATFORM_SUMMARIZE")
LLM_PLATFORM_SUMMARIZE = os.getenv("LLM_PLATFORM_SUMMARIZE")
OCR_PLATFORM_FREQUENCY_AND_SUMMARY = os.getenv("OCR_PLATFORM_FREQUENCY_AND_SUMMARY")
LLM_PLATFORM_FREQUENCY_AND_SUMMARY = os.getenv("LLM_PLATFORM_FREQUENCY_AND_SUMMARY")
LLM_PLATFORM_EXTRACT_HTML_FIELDS = os.getenv("LLM_PLATFORM_EXTRACT_HTML_FIELDS")
EMBEDDING_PLATFORM_EXTRACT_HTML_FIELDS = os.getenv(
    "EMBEDDING_PLATFORM_EXTRACT_HTML_FIELDS"
)
OCR_PLATFORM_EXTRACT_PDF_FIELDS = os.getenv("OCR_PLATFORM_EXTRACT_PDF_FIELDS")
LLM_PLATFORM_EXTRACT_PDF_FIELDS = os.getenv("LLM_PLATFORM_EXTRACT_PDF_FIELDS")
EMBEDDING_PLATFORM_EXTRACT_PDF_FIELDS = os.getenv(
    "EMBEDDING_PLATFORM_EXTRACT_PDF_FIELDS"
)
LLM_PLATFORM_SUMMARIZE_VOB = os.getenv("LLM_PLATFORM_SUMMARIZE_VOB")
EMBEDDING_PLATFORM_EMBED_QUERY = os.getenv("EMBEDDING_PLATFORM_EMBED_QUERY")

# Default Cloud Platforms
DEFAULT_LLM_PLATFORM = os.getenv("DEFAULT_LLM_PLATFORM", "azure")
DEFAULT_EMBEDDING_PLATFORM = os.getenv("DEFAULT_EMBEDDING_PLATFORM", "azure")
DEFAULT_OCR_TYPE = os.getenv("DEFAULT_OCR_TYPE", "azure")

# Database
DISABLE_DATABASE_REPORTING = os.getenv("DISABLE_DATABASE_REPORTING", "false").lower()
DATABASE_URL = os.getenv("DATABASE_URL")

# Other
INSTANCE_TYPE = os.getenv("INSTANCE_TYPE", "local")
X_API_KEY = os.getenv("X_API_KEY")
PADDLE_OCR_URL = os.getenv(
    "PADDLE_OCR_URL",
    "https://46ivdlqmbtqi3prhgg2uppxi4y0ckptd.lambda-url.ap-southeast-2.on.aws",
)
DPI = int(os.getenv("DPI", "200"))
DATA_POINT_UNIT_SEPARATOR = os.getenv("DATA_POINT_UNIT_SEPARATOR", "||")

LINE_TOLERANCE = int(os.getenv("LINE_TOLERANCE", "5"))
SUMMARY_TEMPERATURE = float(os.getenv("SUMMARY_TEMPERATURE", "0.1"))
SUMMARY_SEED = int(os.getenv("SUMMARY_SEED", "42"))

# RAG PROMPTS
RAG_ENABLED = os.getenv("RAG_ENABLED", "true")
RAG_SYSTEM_PROMPT = os.getenv(
    "RAG_SYSTEM_PROMPT",
    """You are a helpful assistant, you will use the provided \
document to answer user questions. If the answer is not found in the the provided \
document, don't make up the answer. You MUST respond strictly in JSON format, \
and don't include any extra text.""",
)
EXTRACT_FIELDS_SYSTEM_PROMPT = os.getenv(
    "EXTRACT_FIELDS_SYSTEM_PROMPT",
    """You are a helpful assistant that answers user questions based **only** on the provided document.

### Guidelines:

- **Extract measurements:** For height and weight, strictly always use standardized short-form units:
  - **Height:** `cm`, `m`, `in`, `ft`
  - **Weight:** `kg`, `g`, `lbs`, `oz`

- **Extract compound units if present:** If a measurement consists of multiple units (e.g., `5 ft 10 in`, `3 kg 500 g`), preserve both components in the extracted data.

- **Avoid repetition:** Do **not** duplicate or recalculate the same measurement in different unit combinations.
  - ✅ Example (correct): `"height": "5 ft 10 in"`
  - ❌ Example (avoid): `"height": "5 ft 10 in (70 in)"`

- If units are not explicitly provided in the document, analyze numerical values carefully, considering it's human height or body weight or size, to infer realistic units from the above standardized short-form units.

- **Accuracy:** If the answer cannot be found explicitly in the provided document, don't make up the answer.

- **Format:** Respond strictly in JSON format, with no additional text.""",
)
RAG_QUERY_PROMPT = os.getenv(
    "RAG_QUERY_PROMPT", "Patient referral document: {context} \n\n {question}"
)

RAG_USER_QUESTION = os.getenv("RAG_USER_QUESTION", "User Question: {question}")
RAG_JSON_PROMPT = os.getenv(
    "RAG_JSON_PROMPT",
    """Strictly follow the above provided document, read the document \
carefully to extract and fill the following JSON. If not found, write None in JSON. \
ONLY REPLY WITH JSON and don't write other text: {json_to_fill}""",
)
RAG_SIMPLE_PROMPT = os.getenv(
    "RAG_SIMPLE_PROMPT",
    """Strictly follow the provided document and answer the user's question. \
If the information is not available in the provided document, inform the user and \
don't make up an answer.""",
)

# Markdown to HTML Prompts
MD_TO_HTML_SYSTEM_PROMPT = os.getenv(
    "MD_TO_HTML_SYSTEM_PROMPT",
    """You are a smart assistant that takes an input of markdown \
formatted text, convert it into HTML Format and strictly return in a JSON without \
any other text.""",
)
MD_TO_HTML_PROMPT = os.getenv(
    "MD_TO_HTML_PROMPT",
    """{md_json}\nTake the provided JSON with markdown format text \
and convert it into HTML format and fill in the following JSON:\n{json_to_fill}\n\
Ensure that all Markdown elements are properly converted to their corresponding HTML \
tags without missing any data. Only reply with JSON and don't write other text.""",
)

# Summarization Default Prompts
DEFAULT_SUMMARY_SYSTEM_PROMPT = os.getenv(
    "DEFAULT_SUMMARY_SYSTEM_PROMPT",
    """Act as a Skilled Nursing healthcare expert with deep clinical understanding and \
assist in the Skilled Nursing referral decisioning process.""",
)
DEFAULT_SUMMARY_PROMPT = os.getenv(
    "DEFAULT_SUMMARY_PROMPT",
    """Strictly follow the provided patient document: {docs}\n\nSummarize the main key points \
with a focus on (but not limited to) patient details on specific clinical conditions, \
measurable rehabilitation needs, and any behavioral health concerns. Ensure that the \
summary highlights skillable needs that support eligibility for skilled nursing care, \
such as Activities of Daily Living (ADL) scores, rehabilitation goals, and any relevant \
patient history. Don't make up any details that are not in the provided documents.""",
)
DEFAULT_REFINE_SUMMARY_PROMPT = os.getenv(
    "DEFAULT_REFINE_SUMMARY_PROMPT",
    """Your job is to produce a final summary with a focus on (but not limited to) patient \
details on specific clinical conditions, measurable rehabilitation needs, and any \
behavioral health concerns. Ensure that the summary highlights skillable needs that \
support eligibility for skilled nursing care, such as Activities of Daily Living \
(ADL) scores, rehabilitation goals, and any relevant patient history. Don't make up \
any details that are not in the provided documents.\n\nWe have provided an existing \
summary up to a certain point:\n{existing_summary}.\n\nWe have the opportunity to refine the \
existing summary (only if needed) with some more context below.\n------------\n{docs}\n\
------------\n\nGiven the new context, refine the original summary. If the context isn't \
useful, return the original summary.""",
)
DEFAULT_MAP_SUMMARY_PROMPT = os.getenv(
    "DEFAULT_MAP_SUMMARY_PROMPT",
    """Strictly follow the provided a set of patient documents: {docs}\n\nSummarize the \
main key points with a focus on (but not limited to) patient details on specific \
clinical conditions, measurable rehabilitation needs, and any behavioral health concerns. \
Ensure that the summary highlights skillable needs that support eligibility for skilled \
nursing care, such as Activities of Daily Living (ADL) scores, rehabilitation goals, and \
any relevant patient history. Don't make up any details that are not in the provided \
documents.""",
)
DEFAULT_REDUCE_SUMMARY_PROMPT = os.getenv(
    "DEFAULT_REDUCE_SUMMARY_PROMPT",
    """Strictly follow the provided set of summaries: {docs}\n\nIf the summaries are related to \
patient referral document then take these and create a final summary in the storytelling \
format otherwise say it's not related to patient referral. Ensure that the summary provides \
specific and measurable data points supporting the need for skilled nursing care. Include details \
about the patient’s clinical needs and conditions, focusing on skillable requirements that qualify \
for skilled nursing services. Clarify any behavioral health concerns, such as suicidal ideation (SI), \
including the patient's SI history, to assess potential danger to self or others, as SNFs cannot admit \
patients whose primary need is behavioral health treatment.\n\nAlso, ensure that the summary clearly \
articulates rehabilitation goals, such as distance for ambulation, and include the patient’s Activities \
of Daily Living (ADL) scores if available. These data points should support the MDS/PDPM scoring process \
and help determine the patient’s eligibility for skilled care. \n\nAdditionally, summarize follow-up \
appointments or post-discharge appointments scheduled or to be scheduled.\n\nDon't make up any details \
that are not in the provided in the summaries and also avoid any initial headings such as Patient Summary, \
or Storytelling Summary etc.""",
)
DEFAULT_VOB_SUMMARY_SYSTEM_PROMPT = os.getenv(
    "DEFAULT_VOB_SUMMARY_SYSTEM_PROMPT",
    """You are an expert in X12 271 healthcare eligibility and benefits verification, with deep knowledge of insurance data extraction, payer hierarchy, and billing sequences. Your objective is to extract, analyze, and summarize all insurance coverage details from X12 271 documents relevant to Skilled Nursing Facility (SNF) admissions.

**Guidelines:**
- **Accurate Code Mapping:** Map all X12 codes (e.g., EB01, EB03, DTP codes) to their human-readable descriptions according to our internal standards. Handle composite fields and variable-length segments gracefully.
- **Clarity and Precision:** Your output must empower SNF staff with technical details for reimbursement decisions.""",
)
DEFAULT_VOB_SUMMARY_PROMPT = os.getenv(
    "DEFAULT_VOB_SUMMARY_PROMPT",
    """X12 271 document: {context}

Prompt: You are an expert in X12 insurance data extraction and summarization. Your objective is to extract all relevant insurance data from a patient’s X12 document to generate a complete insurance picture. This summary should clearly show the hierarchy of payers, the billing sequence, and the patient’s out-of-pocket costs.

---

## **Requirements**

### **1. Data Extraction**
Pull all available data from the X12 file, including:
- Insurance payer details (e.g., Medicare Advantage, traditional Medicare, commercial plans, Medicaid)
- Patient information
- Benefit details (copays, deductibles, coinsurance, etc.)

### **2. Payer Hierarchy & Billing Sequence**
Identify and prioritize the payers correctly:
- If a **Medicare Advantage (Part C)** plan exists, it replaces traditional Medicare (CMS) as the **primary payer**.
- If no Part C plan is found, assume **traditional Medicare (CMS) is primary** unless another commercial plan is listed.
- **Medicaid is always the payer of last resort.**
- If multiple payers exist, display them in the correct **Coordination of Benefits (COB) order**.

### **3. Financial Details**
Extract and summarize the **patient’s out-of-pocket costs**, including:
- Copays
- Deductibles
- Coinsurance
- Any applicable cost-sharing details

### **4. Output**
Provide a structured summary that includes:
- **A detailed, technical breakdown** for internal use (e.g., for billing and reimbursement decisions).

### **5. Handling Missing Information**
- If any required information is missing from the X12 document, do not make assumptions.

---

## **Goal**
Ultimately, the aim is to have an **AI system** that can automatically review a patient’s entire set of insurance data and generate a **comprehensive summary document** showing:
- **Payer hierarchy**
- **Billing sequence**
- **Out-of-pocket costs**

Focus on **accurately extracting and organizing the available data** from the X12 file.""",
)
# LLM Reasoning Configuration (Only works with Azure for now)
DEFAULT_LLM_REASONING_ENABLED = os.getenv("DEFAULT_LLM_REASONING_ENABLED", "false")
LLM_REASONING_ENABLED_QA = os.getenv("LLM_REASONING_ENABLED_QA")
LLM_REASONING_ENABLED_SUMMARIZE = os.getenv("LLM_REASONING_ENABLED_SUMMARIZE")
LLM_REASONING_ENABLED_FREQUENCY_AND_SUMMARY = os.getenv(
    "LLM_REASONING_ENABLED_FREQUENCY_AND_SUMMARY"
)
LLM_REASONING_ENABLED_EXTRACT_PDF_FIELDS = os.getenv(
    "LLM_REASONING_ENABLED_EXTRACT_PDF_FIELDS"
)
LLM_REASONING_ENABLED_EXTRACT_HTML_FIELDS = os.getenv(
    "LLM_REASONING_ENABLED_EXTRACT_HTML_FIELDS"
)
LLM_REASONING_ENABLED_SUMMARIZE_VOB = os.getenv("LLM_REASONING_ENABLED_SUMMARIZE_VOB")

# QA Default Prompt
QA_SYSTEM_PROMPT = os.getenv(
    "QA_SYSTEM_PROMPT",
    """You are acting as a Skilled Nursing Facility Intake Manager. Your primary role is to analyze the provided patient referral documents step-by-step and respond to user questions to assist in the decision-making process for accepting or rejecting referrals. Follow these guidelines strictly:

1. **Step-by-step Reasoning**: Analyze the patient referral document step-by-step to infer the answers from the provided patient referral document.
2. **Document-based Responses Only**: If an answer cannot be inferred from the document, do not create or assume information.
3. **JSON Format Only**: Respond strictly in following JSON format: {"recommendation":"","recommendationReason":""}
4. The 'recommendation' must be one of the following:
    - "Yes": If the answer can be inferred from the document and supports a positive recommendation.
    - "No": If the answer can be confidently inferred from the document, supporting a negative recommendation. Consider "No" if you have verified that the relevant section (where the information *should* appear) is indeed present and does not mention the queried disease/condition, treat this as a negative finding.
    - "Inconclusive": If you cannot confirm you looked in the correct place for that information.
5. The 'recommendationReason' must provide a clear and concise justification for the recommendation, using only the information available in the provided document.
    - If the recommendation is "No" or "Inconclusive" due to insufficient information in the referral document, append the following at the end of recommendationReason: "Please ensure the entire medical history has been uploaded to ReferralPlus."
6. **No Extra Text**: Do not include any explanatory text outside the JSON response.

Ensure the response is accurate and only uses the data provided in the patient referral document.""",
)
DEFAULT_QA_PROMPT = os.getenv(
    "DEFAULT_QA_PROMPT",
    """Strictly analyze the provided patient referral document step-by-step and answer the user's question, adhering to the following guidelines:

1. Act as an expert in Medical Domain, don't look for only explicit answers instead use your knowledge and try to infer the answer from given patient data.
2. Include the relevant reason for your recommendation.
3. Consider the relevance of past medical issues (resolved or ongoing) to the user’s question. Exclude resolved issues unless they provide critical context.""",
)
DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT = os.getenv(
    "DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT",
    """You are a highly accurate and detail-oriented medical document classification assistant. Your task is to identify the type of a medical documents based on its content. These documents are used for making clinical decisions about admitting patients to skilled nursing facilities.

You must analyze the semantics of the content and classify it into the following **strictly defined categories**:

## Document Categories:

{DOCUMENT_CATEGORIES}""",
)
DEFAULT_DOCUMENT_CATEGORIZATION_PROMPT = os.getenv(
    "DEFAULT_DOCUMENT_CATEGORIZATION_PROMPT",
    """You are a highly experienced medical expert specializing in patient admissions and referrals for skilled nursing facilities. Your task is to strictly analyze the content of the provided patient referral document step-by-step and accurately classify it into predefined medical document categories.

### 🧪 Guidelines:

1. **Act as a medical expert** with deep domain knowledge of referral and admission documentation.
2. Use **clinical reasoning**: Don’t rely solely on explicit headings or labels—use contextual clues from symptoms, diagnoses, treatments, terminology, and narrative descriptions.
3. Provide a **well-justified explanation** of the classification with references to supporting evidence or inferred patterns.
4. Output your classification strictly with:
   - `documentCategory`: One of the listed categories.
   - `reasoning`: A clear and detailed explanation of why the document fits this category.
   - `confidenceScore`: A float between 0 and 1 indicating how confident you are in the classification.

### ✅ Example Output:
{
  "documentCategory": "History and Physical (H&P)",
  "reasoning": "The document contains the patient's chief complaint, a detailed history of present illness, past medical history, and findings from a physical examination. These are characteristic components of an H&P document.",
  "confidenceScore": 0.94
}
""",
)

# Query Expansion
NUMBER_OF_DISTINCT_VARIANTS = int(os.getenv("NUMBER_OF_DISTINCT_VARIANTS", "2"))
NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT = int(
    os.getenv("NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT", "20")
)
QUERY_EXPANSION_SYSTEM_PROMPT = os.getenv(
    "QUERY_EXPANSION_SYSTEM_PROMPT",
    f"""You are an SNF Admission Coordinator responsible for evaluating patient referrals for \
intake or rejection decisions. Your task is to expand clinical questions by considering \
different aspects or details that might be relevant to the decision-making process and \
retrieving correct information. Each variation should be a unique perspective with the \
goal of retrieving right documents using semantic search.

Guidelines:
1. Consider the underlying purpose of the question and what specific details might matter \
in evaluating the patient.
2. Provide {NUMBER_OF_DISTINCT_VARIANTS} distinct variations of the question that reflect \
different angles or aspects of inquiry. Variations should not merely rephrase but also expand \
on related details.
3. All variations must be clear, contextually relevant, and medically accurate.

Example Input Question: Does the patient have morbid obesity?

Example Expanded Queries:
1. "What is the patient’s BMI, and does it fall into the morbid obesity category?"
2. "Does the patient’s weight condition require specialized equipment or accommodations?

Think step by step to analyze the question and then generate the variations.""",
)

# Document Reranking
DISABLE_DOCUMENT_RERANKING = os.getenv("DISABLE_DOCUMENT_RERANKING", "true").lower()
DISABLE_DOCUMENT_RERANKING_AUTH = os.getenv(
    "DISABLE_DOCUMENT_RERANKING_AUTH", "false"
).lower()
RERANKING_API_URL = os.getenv(
    "RERANKING_API_URL",
    "https://hdljs5meazsson5sft2jfdknru0jopgi.lambda-url.us-east-1.on.aws/rerank",
)
NUMBER_OF_CHUNKS_FOR_LLM = int(os.getenv("NUMBER_OF_CHUNKS_FOR_LLM", "10"))
RERANKING_SERVICE_TIMEOUT = int(os.getenv("RERANKING_SERVICE_TIMEOUT", "600"))
RERANKING_AUTH_KEY = os.getenv("RERANKING_AUTH_KEY")

# OCR, LLM & EMBEDDING COSTS
DOCUMENT_INTELLIGENCE_COST_PER_1K_PAGES = float(
    os.getenv("DOCUMENT_INTELLIGENCE_COST_PER_1K_PAGES", "1.50")
)
AWS_TEXTRACT_COST_PER_1K_PAGES = float(
    os.getenv("AWS_TEXTRACT_COST_PER_1K_PAGES", "1.50")
)
GPT_4O_MINI_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("GPT_4O_MINI_COST_PER_1K_INPUT_TOKENS", "0.00015")
)
GPT_4O_MINI_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("GPT_4O_MINI_COST_PER_1K_OUTPUT_TOKENS", "0.0006")
)
GPT_4_1_MINI_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("GPT_4_1_MINI_COST_PER_1K_INPUT_TOKENS", "0.0004")
)
GPT_4_1_MINI_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("GPT_4_1_MINI_COST_PER_1K_OUTPUT_TOKENS", "0.0016")
)
GPT_O1_MINI_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("GPT_O1_MINI_COST_PER_1K_INPUT_TOKENS", "0.0011")
)
GPT_O1_MINI_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("GPT_O1_MINI_COST_PER_1K_OUTPUT_TOKENS", "0.0044")
)
GPT_O3_MINI_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("GPT_O3_MINI_COST_PER_1K_INPUT_TOKENS", "0.0011")
)
GPT_O3_MINI_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("GPT_O3_MINI_COST_PER_1K_OUTPUT_TOKENS", "0.0044")
)
TEXT_EMBEDDING_3_LARGE_COST_PER_1K_TOKENS = float(
    os.getenv("TEXT_EMBEDDING_3_LARGE_COST_PER_1K_TOKENS", "0.00013")
)
TEXT_EMBEDDING_3_SMALL_COST_PER_1K_TOKENS = float(
    os.getenv("TEXT_EMBEDDING_3_SMALL_COST_PER_1K_TOKENS", "0.00002")
)
CLAUDE_3_HAIKU_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("CLAUDE_3_HAIKU_COST_PER_1K_INPUT_TOKENS", "0.00025")
)
CLAUDE_3_HAIKU_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("CLAUDE_3_HAIKU_COST_PER_1K_OUTPUT_TOKENS", "0.00125")
)
CLAUDE_3_5_HAIKU_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("CLAUDE_3_5_HAIKU_COST_PER_1K_INPUT_TOKENS", "0.0008")
)
CLAUDE_3_5_HAIKU_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("CLAUDE_3_5_HAIKU_COST_PER_1K_OUTPUT_TOKENS", "0.004")
)
CLAUDE_3_SONNET_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("CLAUDE_3_SONNET_COST_PER_1K_INPUT_TOKENS", "0.003")
)
CLAUDE_3_SONNET_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("CLAUDE_3_SONNET_COST_PER_1K_OUTPUT_TOKENS", "0.015")
)
CLAUDE_3_5_SONNET_COST_PER_1K_INPUT_TOKENS = float(
    os.getenv("CLAUDE_3_5_SONNET_COST_PER_1K_INPUT_TOKENS", "0.003")
)
CLAUDE_3_5_SONNET_COST_PER_1K_OUTPUT_TOKENS = float(
    os.getenv("CLAUDE_3_5_SONNET_COST_PER_1K_OUTPUT_TOKENS", "0.015")
)
AMAZON_TITAN_EMBED_V2_COST_PER_1K_TOKENS = float(
    os.getenv("AMAZON_TITAN_EMBED_V2_COST_PER_1K_TOKENS", "0.00002")
)

# Document Categorization Configuration
DOCUMENT_CATEGORIZATION_FUZZY_MATCH_THRESHOLD = int(
    os.getenv("DOCUMENT_CATEGORIZATION_FUZZY_MATCH_THRESHOLD", "95")
)
DOCUMENT_CATEGORIZATION_KEYWORD_MATCH_PERCENTAGE = float(
    os.getenv("DOCUMENT_CATEGORIZATION_KEYWORD_MATCH_PERCENTAGE", "0.4")
)
DOCUMENT_CATEGORIZATION_MATCH_WEIGHT = int(
    os.getenv("DOCUMENT_CATEGORIZATION_MATCH_WEIGHT", "60")
)
DOCUMENT_CATEGORIZATION_LLM_WEIGHT = int(
    os.getenv("DOCUMENT_CATEGORIZATION_LLM_WEIGHT", "40")
)
DOCUMENT_CATEGORIZATION_TOTAL_SCORE_THRESHOLD = int(
    os.getenv("DOCUMENT_CATEGORIZATION_TOTAL_SCORE_THRESHOLD", "30")
)

DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED = [
    {
        "documentCategory": "Hospital Discharge Summary",
        "categoryPrompt": "Describe the patient's status at discharge, reason for admission, major findings, treatment course, procedures performed, and follow-up or discharge instructions",
        "categoryKeywords": [
            "discharge summary",
            "discharge plan",
            "discharge diagnosis",
            "discharge date",
            "discharged",
            "discharge instructions",
            "hospital course",
            "admission date",
            "discharge medications",
        ],
    },
    {
        "documentCategory": "Physician Orders and Progress Notes",
        "categoryPrompt": "Include formal orders by doctors or notes on patient condition or treatment updates",
        "categoryKeywords": [
            "physician order",
            "progress note",
            "doctor's note",
            "medical order",
            "treatment plan",
            "clinical progress",
            "physician's assessment",
            "doctor's assessment",
            "progress report",
            "clinical note",
        ],
    },
    {
        "documentCategory": "History and Physical (H&P)",
        "categoryPrompt": "Contain chief complaint, history of present illness, past medical history, physical examination, and initial assessment",
        "categoryKeywords": [
            "history and physical",
            "H&P",
            "chief complaint",
            "present illness",
            "past medical history",
            "physical examination",
            "review of systems",
            "family history",
            "social history",
            "vital signs",
            "medical history",
        ],
    },
    {
        "documentCategory": "Nursing Assessments",
        "categoryPrompt": "Include nursing observations, vital signs, pain scales, and assessments upon admission",
        "categoryKeywords": [
            "nursing assessment",
            "nursing note",
            "nurse's note",
            "nursing care plan",
            "nursing intervention",
            "nursing observation",
            "nursing documentation",
            "nursing evaluation",
            "nursing diagnosis",
            "patient assessment",
        ],
    },
    {
        "documentCategory": "Rehabilitation and Therapy Evaluations",
        "categoryPrompt": "Include physical therapy, occupational therapy, or speech therapy evaluation reports",
        "categoryKeywords": [
            "rehabilitation",
            "therapy evaluation",
            "physical therapy",
            "occupational therapy",
            "speech therapy",
            "therapy assessment",
            "therapy plan",
            "therapy goals",
            "therapy progress",
            "rehabilitation plan",
            "functional assessment",
        ],
    },
    {
        "documentCategory": "Diagnostic Reports and Test Results",
        "categoryPrompt": "Contain lab results, radiology reports, imaging summaries, or pathology findings",
        "categoryKeywords": [
            "diagnostic report",
            "test result",
            "laboratory result",
            "lab result",
            "imaging report",
            "radiology report",
            "pathology report",
            "diagnostic test",
            "laboratory test",
            "diagnostic imaging",
            "lab values",
            "test findings",
        ],
    },
    {
        "documentCategory": "Medication Administration Record (MAR) and Medication Reconciliation",
        "categoryPrompt": "Contain detailed medication history, administration logs, or reconciliation between prescriptions",
        "categoryKeywords": [
            "medication administration record",
            "MAR",
            "medication reconciliation",
            "medication list",
            "drug administration",
            "medication history",
            "prescription record",
            "medication dosage",
            "drug therapy",
            "medication schedule",
        ],
    },
    {"documentCategory": "Other", "categoryPrompt": "", "categoryKeywords": []},
]

DEFAULT_DOCUMENT_CATEGORIES_CONFIG = os.getenv(
    "DEFAULT_DOCUMENT_CATEGORIES_CONFIG",
    DEFAULT_DOCUMENT_CATEGORIES_IF_NOT_SPECIFIED,
)

FREQUENCY_WORDS_CONTEXT = int(os.getenv("FREQUENCY_WORDS_CONTEXT", "10"))
FREQUENCY_SYSTEM_PROMPT = os.getenv(
    "FREQUENCY_SYSTEM_PROMPT",
    """You are a clinical text analysis assistant. Your task is to assess whether a specific **keyword** mentioned in a medical or contextual sentence reflects a **genuine positive occurrence** (affirmed or currently applicable) or a **false/negated/absent mention** (e.g., denial, negation, historical but no longer applicable).

Carefully analyze each sentence using clear clinical logic and apply the labeling rules precisely.

Respond strictly using the following JSON format:
{
  "positive_mention": "true" // or "false"
}""",
)
DEFAULT_FREQUENCY_PROMPT = os.getenv(
    "DEFAULT_FREQUENCY_PROMPT",
    """
Analyze whether mentions of the keyword `{keyword}` in the following context text represent a genuine positive occurrence or a false/negated mention.

## Context Text:
`{context}`

## Guidelines:

- **TRUE** (`positive_mention: true`):  
  The keyword is **affirmed** or **currently applicable**.  
  _Examples:_  
  - "Patient has a history of smoking" → ✅ **TRUE**  
  - "Continues to smoke daily" → ✅ **TRUE**

- **FALSE** (`positive_mention: false`):  
  The keyword is **denied**, **negated**, **no longer present**, or explicitly stated as **absent**.  
  _Examples:_  
  - "Patient denies smoking" → ❌ **FALSE**  
  - "No evidence of smoking" → ❌ **FALSE**  
  - "Patient quit smoking 10 years ago" → ❌ **FALSE**

## Return only the result in this JSON format:
{
  "positive_mention": "true"
}""",
)
