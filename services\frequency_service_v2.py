from rapidfuzz import fuzz
import logging
from typing import Dict, List, Any, Union
import asyncio
from utils import get_cloud_object, extract_json_with_regex, convert_text_to_dict
from services.frequency_service import (
    check_if_box_overlap,
    split_box_text_into_words,
)
from constants import (
    FREQUENCY_SYSTEM_PROMPT,
    FREQUENCY_WORDS_CONTEXT,
    DEFAULT_FREQUENCY_PROMPT,
)
from services.cloud_services.aws import AWSHelper
from services.cloud_services.azure import AzureHelper


async def get_keyword_count_v2(
    document_json: Dict[str, Any],
    keywords: str,
    frequency_prompt: str = DEFAULT_FREQUENCY_PROMPT,
    llm_platform: str = "azure",
    llm_reasoning_enabled: str = "false",
) -> Dict[str, Any]:
    """
    Get the keyword count with context analysis to filter out false positives.
    Args:
        document_json (Dict[str, Any]): The document text JSON.
        keywords_str (str): The comma-separated keywords.
        frequency_prompt (str): The prompt for AI on how to analyze the context for each keyword.
        llm_platform (str): The LLM platform to use for context analysis.
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: The keyword count with context analysis.
    """
    keywords = keywords.split(",")
    logging.info(f"keywords: {keywords}")

    # Remove :,- from the keywords
    keywords = [
        keyword.strip().lower().translate(str.maketrans("", "", ":,-"))
        for keyword in keywords
    ]

    document_json = split_box_text_into_words(document_json)

    # First, get all potential keyword occurrences
    potential_matches = await count_keyword_occurrences_with_context(
        document_json, keywords
    )

    # Then analyze context for each keyword to filter out false positives
    analyzed_matches = await analyze_keyword_contexts(
        potential_matches, frequency_prompt, llm_platform, llm_reasoning_enabled
    )

    freqs = {}
    freqs["keywords"] = analyzed_matches

    return freqs


async def count_keyword_occurrences_with_context(
    document_json: Dict[str, Any], keywords: List[str], matching_threshold: int = 88
) -> Dict[str, Any]:
    """
    Count the keyword occurrences and collect context for each occurrence.
    Args:
        document_json (Dict[str, Any]): The document text JSON.
        keywords (List[str]): The keywords.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        Dict[str, Any]: The keyword count with context information.
    """
    keywords_freq = {}

    all_words = []
    for page_num in document_json:
        if "usageCost" in page_num:
            continue
        data = document_json[page_num]["info"]
        page_data = {
            "image_size": document_json[page_num]["image_size"],
            "angle": document_json[page_num]["angle"],
            "extraction": document_json[page_num]["extraction"],
        }

        # Add each word with its page number and position in the document
        for i, word_info in enumerate(data):
            all_words.append(
                {
                    "word": word_info[1].lower(),
                    "page_num": page_num,
                    "position": i,
                    "box": word_info[0],
                    "page_data": page_data,
                }
            )

    # For each keyword, find matches and collect context
    for keyword in keywords:
        keyword_len = len(keyword.split())

        # If keyword not found, add it with zero count
        if keyword not in keywords_freq:
            keywords_freq[keyword] = {
                "Counts": 0,
                "Pages": {},
                "Contexts": [],
            }

        # Check for matches using sliding window
        for i in range(len(all_words) - keyword_len + 1):
            # Skip if words are from different pages
            if not all(
                all_words[i]["page_num"] == all_words[i + j]["page_num"]
                for j in range(keyword_len)
            ):
                continue

            # Construct the candidate phrase
            candidate = " ".join(all_words[i + j]["word"] for j in range(keyword_len))
            candidate = candidate.translate(str.maketrans("", "", ":,-"))

            # Check similarity
            similarity_score = fuzz.ratio(keyword, candidate.strip())
            if similarity_score > matching_threshold:
                page_num = all_words[i]["page_num"]

                # Extract context (FREQUENCY_WORDS_CONTEXT words before and after)
                context_start = max(0, i - FREQUENCY_WORDS_CONTEXT)
                context_end = min(
                    len(all_words), i + keyword_len + FREQUENCY_WORDS_CONTEXT
                )

                # Ensure we're not crossing page boundaries for context
                context_words = []
                for j in range(context_start, context_end):
                    if j < len(all_words) and all_words[j]["page_num"] == page_num:
                        context_words.append(all_words[j]["word"])

                context_text = " ".join(context_words)
                keyword_position = " ".join(
                    context_words[i - context_start : i - context_start + keyword_len]
                )

                # Store the match with its context
                if page_num not in keywords_freq[keyword]["Pages"]:
                    keywords_freq[keyword]["Pages"][page_num] = {
                        "Counts": 1,
                        "Boxes": [all_words[i]["box"]],
                        "page_data": all_words[i]["page_data"],
                    }
                else:
                    # Check if the bounding box overlaps with existing ones
                    result = check_if_box_overlap(
                        keywords_freq[keyword]["Pages"][page_num]["Boxes"],
                        all_words[i]["box"],
                    )
                    if not result[0]:  # If no overlap
                        keywords_freq[keyword]["Pages"][page_num]["Counts"] += 1
                        keywords_freq[keyword]["Pages"][page_num]["Boxes"].append(
                            all_words[i]["box"]
                        )

                # Add context information
                keywords_freq[keyword]["Contexts"].append(
                    {
                        "text": context_text,
                        "keyword": keyword_position,
                        "page": page_num,
                        "box": all_words[i]["box"],
                    }
                )

                # Increment the total count
                keywords_freq[keyword]["Counts"] += 1

    # Add keywords that weren't found
    not_in_dict = [item for item in keywords if item not in keywords_freq]
    for kw in not_in_dict:
        keywords_freq[kw] = {
            "Counts": 0,
            "Pages": None,
            "Contexts": [],
        }

    return keywords_freq


async def analyze_keyword_contexts(
    keywords_freq: Dict[str, Any],
    frequency_prompt: str = DEFAULT_FREQUENCY_PROMPT,
    llm_platform: str = "azure",
    llm_reasoning_enabled: str = "false",
) -> Dict[str, Any]:
    """
    Analyze the context of each keyword occurrence to determine if it's a positive or negative mention.
    Args:
        keywords_freq (Dict[str, Any]): The keyword frequency data with contexts.
        frequency_prompt (str): The prompt for AI on how to analyze the context for each keyword.
        llm_platform (str): The LLM platform to use.
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict[str, Any]: The updated keyword frequency data with positive/negative analysis.
    """
    # Get the appropriate LLM object based on the platform
    llm_object = get_cloud_object(llm_platform)

    # Process each keyword
    for keyword, data in keywords_freq.items():
        if data["Counts"] == 0 or not data["Contexts"]:
            continue

        # Prepare contexts for LLM analysis
        contexts = [ctx["text"] for ctx in data["Contexts"]]

        # Process all contexts at once
        results = await analyze_contexts_with_llm(
            keyword, contexts, frequency_prompt, llm_object, llm_reasoning_enabled
        )

        # Mark which contexts were positive
        for i, is_positive in enumerate(results["is_positive"]):
            if i < len(data["Contexts"]):
                data["Contexts"][i]["is_positive"] = is_positive

        # Create a list of positive contexts for easy access
        data["PositiveContexts"] = [
            ctx
            for i, ctx in enumerate(data["Contexts"])
            if i < len(results["is_positive"]) and results["is_positive"][i]
        ]

    return keywords_freq


async def analyze_contexts_with_llm(
    keyword: str,
    contexts: List[str],
    frequency_prompt: str,
    llm_object: Union[AzureHelper, AWSHelper],
    llm_reasoning_enabled: str,
) -> Dict[str, Any]:
    """
    Use LLM to analyze if keyword mentions in contexts are positive or negative.
    Args:
        keyword (str): The keyword being analyzed.
        contexts (List[str]): List of context strings containing the keyword.
        llm_object (Union[AzureHelper, AWSHelper]): The LLM service object (AWS or Azure).
        llm_reasoning_enabled (str): Whether to enable LLM reasoning.
    Returns:
        Dict: Results of the analysis including positive count and list of positive flags.
    """
    return {"positive_mention": "true"}
    prompt = frequency_prompt.format(keyword=keyword, context=" ".join(contexts))
    response = llm_object.get_llm_response_with_metadata(
        prompt,
        "json_object",
        FREQUENCY_SYSTEM_PROMPT,
        llm_reasoning_enabled,
    )

    llm_response = response.get("content", "")
    try:
        response = convert_text_to_dict(llm_response)
    except Exception as e:
        logging.exception(
            f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
        )
        response = extract_json_with_regex(llm_response)

    return response
